{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-03-2025_11-22-06] - 开始训练，配置参数如下：
[Aug-03-2025_11-22-06] - early_stop_enabled: True
[Aug-03-2025_11-22-06] - model: FMLP
[Aug-03-2025_11-22-06] - lr: 0.001
[Aug-03-2025_11-22-06] - batch_size: 128
[Aug-03-2025_11-22-06] - neg_num: 99
[Aug-03-2025_11-22-06] - l2_reg: 0
[Aug-03-2025_11-22-06] - l2_emb: 0.0
[Aug-03-2025_11-22-06] - embed_dim: 50
[Aug-03-2025_11-22-06] - hidden_size: 32
[Aug-03-2025_11-22-06] - dropout: 0.2
[Aug-03-2025_11-22-06] - epochs: 1000000
[Aug-03-2025_11-22-06] - early_stop: 50
[Aug-03-2025_11-22-06] - datapath: ../../data/
[Aug-03-2025_11-22-06] - dataset: ml-100k
[Aug-03-2025_11-22-06] - train_data: ml-100k.txt
[Aug-03-2025_11-22-06] - log_path: ../log
[Aug-03-2025_11-22-06] - num_layers: 2
[Aug-03-2025_11-22-06] - num_heads: 1
[Aug-03-2025_11-22-06] - inner_size: 256
[Aug-03-2025_11-22-06] - max_seq_len: 200
[Aug-03-2025_11-22-06] - upload_mode: full
[Aug-03-2025_11-22-06] - skip_test_eval: True
[Aug-03-2025_11-22-06] - eval_freq: 1
[Aug-03-2025_11-22-06] - use_dynamic_sampling: False
[Aug-03-2025_11-22-06] - norm_first: False
[Aug-03-2025_11-22-06] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-03-2025_11-22-06] - 最大序列长度: 200
[Aug-03-2025_11-22-06] - 批次大小: 128
[Aug-03-2025_11-22-08] - 参数上传模式: full
[Aug-03-2025_11-22-08] - 隐私参数（本地更新）: []
[Aug-03-2025_11-22-08] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'LayerNorm.weight', 'LayerNorm.bias', 'encoder.layer.0.filter_layer.complex_weight', 'encoder.layer.0.feed_forward.dense_1.weight', 'encoder.layer.0.feed_forward.dense_1.bias', 'encoder.layer.0.feed_forward.dense_2.weight', 'encoder.layer.0.feed_forward.dense_2.bias', 'encoder.layer.0.first_layer_norm.weight', 'encoder.layer.0.first_layer_norm.bias', 'encoder.layer.0.second_layer_norm.weight', 'encoder.layer.0.second_layer_norm.bias', 'encoder.layer.1.filter_layer.complex_weight', 'encoder.layer.1.feed_forward.dense_1.weight', 'encoder.layer.1.feed_forward.dense_1.bias', 'encoder.layer.1.feed_forward.dense_2.weight', 'encoder.layer.1.feed_forward.dense_2.bias', 'encoder.layer.1.first_layer_norm.weight', 'encoder.layer.1.first_layer_norm.bias', 'encoder.layer.1.second_layer_norm.weight', 'encoder.layer.1.second_layer_norm.bias']
[Aug-03-2025_11-22-08] - 动态负采样: False
[Aug-03-2025_11-22-08] - 用户数量: 943
[Aug-03-2025_11-22-08] - 物品数量: 1349
