{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-03-2025_11-36-23] - 开始训练，配置参数如下：
[Aug-03-2025_11-36-23] - early_stop_enabled: True
[Aug-03-2025_11-36-23] - model: FMLP
[Aug-03-2025_11-36-23] - lr: 0.001
[Aug-03-2025_11-36-23] - batch_size: 128
[Aug-03-2025_11-36-23] - neg_num: 99
[Aug-03-2025_11-36-23] - l2_reg: 0
[Aug-03-2025_11-36-23] - l2_emb: 0.0
[Aug-03-2025_11-36-23] - embed_dim: 50
[Aug-03-2025_11-36-23] - hidden_size: 32
[Aug-03-2025_11-36-23] - dropout: 0.2
[Aug-03-2025_11-36-23] - epochs: 1000000
[Aug-03-2025_11-36-23] - early_stop: 50
[Aug-03-2025_11-36-23] - datapath: ../../data/
[Aug-03-2025_11-36-23] - dataset: ml-100k
[Aug-03-2025_11-36-23] - train_data: ml-100k.txt
[Aug-03-2025_11-36-23] - log_path: ../log
[Aug-03-2025_11-36-23] - num_layers: 2
[Aug-03-2025_11-36-23] - num_heads: 1
[Aug-03-2025_11-36-23] - inner_size: 256
[Aug-03-2025_11-36-23] - max_seq_len: 200
[Aug-03-2025_11-36-23] - upload_mode: full
[Aug-03-2025_11-36-23] - skip_test_eval: True
[Aug-03-2025_11-36-23] - eval_freq: 1
[Aug-03-2025_11-36-23] - use_dynamic_sampling: False
[Aug-03-2025_11-36-23] - norm_first: False
[Aug-03-2025_11-36-23] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-03-2025_11-36-23] - 最大序列长度: 200
[Aug-03-2025_11-36-23] - 批次大小: 128
[Aug-03-2025_11-36-24] - 参数上传模式: full
[Aug-03-2025_11-36-24] - 隐私参数（本地更新）: []
[Aug-03-2025_11-36-24] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'LayerNorm.weight', 'LayerNorm.bias', 'encoder.layer.0.filter_layer.complex_weight', 'encoder.layer.0.feed_forward.dense_1.weight', 'encoder.layer.0.feed_forward.dense_1.bias', 'encoder.layer.0.feed_forward.dense_2.weight', 'encoder.layer.0.feed_forward.dense_2.bias', 'encoder.layer.0.first_layer_norm.weight', 'encoder.layer.0.first_layer_norm.bias', 'encoder.layer.0.second_layer_norm.weight', 'encoder.layer.0.second_layer_norm.bias', 'encoder.layer.1.filter_layer.complex_weight', 'encoder.layer.1.feed_forward.dense_1.weight', 'encoder.layer.1.feed_forward.dense_1.bias', 'encoder.layer.1.feed_forward.dense_2.weight', 'encoder.layer.1.feed_forward.dense_2.bias', 'encoder.layer.1.first_layer_norm.weight', 'encoder.layer.1.first_layer_norm.bias', 'encoder.layer.1.second_layer_norm.weight', 'encoder.layer.1.second_layer_norm.bias']
[Aug-03-2025_11-36-24] - 动态负采样: False
[Aug-03-2025_11-36-24] - 用户数量: 943
[Aug-03-2025_11-36-24] - 物品数量: 1349
[Aug-03-2025_11-36-41] - epoch:1, time: 10.417792(s), valid (NDCG@10: 0.0530, HR@10: 0.1113), test: SKIPPED, all_time: 10.417792(s)
[Aug-03-2025_11-36-41] - 新的最佳性能: valid NDCG@10=0.0530, valid HR@10=0.1113
[Aug-03-2025_11-36-56] - epoch:2, time: 11.131093(s), valid (NDCG@10: 0.0829, HR@10: 0.1665), test: SKIPPED, all_time: 21.548885(s)
[Aug-03-2025_11-36-56] - 新的最佳性能: valid NDCG@10=0.0829, valid HR@10=0.1665
[Aug-03-2025_11-37-11] - epoch:3, time: 10.390572(s), valid (NDCG@10: 0.1168, HR@10: 0.2354), test: SKIPPED, all_time: 31.939457(s)
[Aug-03-2025_11-37-11] - 新的最佳性能: valid NDCG@10=0.1168, valid HR@10=0.2354
[Aug-03-2025_11-37-26] - epoch:4, time: 10.979334(s), valid (NDCG@10: 0.1594, HR@10: 0.3086), test: SKIPPED, all_time: 42.918791(s)
[Aug-03-2025_11-37-26] - 新的最佳性能: valid NDCG@10=0.1594, valid HR@10=0.3086
[Aug-03-2025_11-37-42] - epoch:5, time: 10.941341(s), valid (NDCG@10: 0.1968, HR@10: 0.3531), test: SKIPPED, all_time: 53.860133(s)
[Aug-03-2025_11-37-42] - 新的最佳性能: valid NDCG@10=0.1968, valid HR@10=0.3531
[Aug-03-2025_11-37-56] - epoch:6, time: 10.301964(s), valid (NDCG@10: 0.2190, HR@10: 0.3934), test: SKIPPED, all_time: 64.162096(s)
[Aug-03-2025_11-37-56] - 新的最佳性能: valid NDCG@10=0.2190, valid HR@10=0.3934
[Aug-03-2025_11-38-11] - epoch:7, time: 10.626343(s), valid (NDCG@10: 0.2424, HR@10: 0.4168), test: SKIPPED, all_time: 74.788440(s)
[Aug-03-2025_11-38-11] - 新的最佳性能: valid NDCG@10=0.2424, valid HR@10=0.4168
[Aug-03-2025_11-38-26] - epoch:8, time: 11.078558(s), valid (NDCG@10: 0.2588, HR@10: 0.4549), test: SKIPPED, all_time: 85.866997(s)
[Aug-03-2025_11-38-26] - 新的最佳性能: valid NDCG@10=0.2588, valid HR@10=0.4549
[Aug-03-2025_11-38-43] - epoch:9, time: 11.925704(s), valid (NDCG@10: 0.2780, HR@10: 0.4772), test: SKIPPED, all_time: 97.792702(s)
[Aug-03-2025_11-38-43] - 新的最佳性能: valid NDCG@10=0.2780, valid HR@10=0.4772
[Aug-03-2025_11-39-00] - epoch:10, time: 11.670707(s), valid (NDCG@10: 0.2805, HR@10: 0.4857), test: SKIPPED, all_time: 109.463409(s)
[Aug-03-2025_11-39-00] - 新的最佳性能: valid NDCG@10=0.2805, valid HR@10=0.4857
[Aug-03-2025_11-39-15] - epoch:11, time: 11.290804(s), valid (NDCG@10: 0.2848, HR@10: 0.4931), test: SKIPPED, all_time: 120.754213(s)
[Aug-03-2025_11-39-15] - 新的最佳性能: valid NDCG@10=0.2848, valid HR@10=0.4931
[Aug-03-2025_11-39-30] - epoch:12, time: 10.358233(s), valid (NDCG@10: 0.2979, HR@10: 0.5027), test: SKIPPED, all_time: 131.112446(s)
[Aug-03-2025_11-39-30] - 新的最佳性能: valid NDCG@10=0.2979, valid HR@10=0.5027
