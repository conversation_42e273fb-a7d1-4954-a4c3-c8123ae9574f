{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'beauty', 'train_data': 'beauty.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-02-2025_22-52-46] - 开始训练，配置参数如下：
[Aug-02-2025_22-52-46] - early_stop_enabled: True
[Aug-02-2025_22-52-46] - model: FMLP
[Aug-02-2025_22-52-46] - lr: 0.001
[Aug-02-2025_22-52-46] - batch_size: 128
[Aug-02-2025_22-52-46] - neg_num: 99
[Aug-02-2025_22-52-46] - l2_reg: 0
[Aug-02-2025_22-52-46] - l2_emb: 0.0
[Aug-02-2025_22-52-46] - embed_dim: 50
[Aug-02-2025_22-52-46] - hidden_size: 32
[Aug-02-2025_22-52-46] - dropout: 0.2
[Aug-02-2025_22-52-46] - epochs: 1000000
[Aug-02-2025_22-52-46] - early_stop: 50
[Aug-02-2025_22-52-46] - datapath: ../../data/
[Aug-02-2025_22-52-46] - dataset: beauty
[Aug-02-2025_22-52-46] - train_data: beauty.txt
[Aug-02-2025_22-52-46] - log_path: ../log
[Aug-02-2025_22-52-46] - num_layers: 2
[Aug-02-2025_22-52-46] - num_heads: 1
[Aug-02-2025_22-52-46] - inner_size: 256
[Aug-02-2025_22-52-46] - max_seq_len: 200
[Aug-02-2025_22-52-46] - upload_mode: full
[Aug-02-2025_22-52-46] - skip_test_eval: True
[Aug-02-2025_22-52-46] - eval_freq: 1
[Aug-02-2025_22-52-46] - use_dynamic_sampling: False
[Aug-02-2025_22-52-46] - norm_first: False
[Aug-02-2025_22-52-46] - 训练数据: ../../data/beauty/beauty.txt
[Aug-02-2025_22-52-46] - 最大序列长度: 200
[Aug-02-2025_22-52-46] - 批次大小: 128
[Aug-02-2025_22-52-49] - 参数上传模式: full
[Aug-02-2025_22-52-49] - 隐私参数（本地更新）: []
[Aug-02-2025_22-52-49] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'LayerNorm.weight', 'LayerNorm.bias', 'encoder.layer.0.filter_layer.complex_weight', 'encoder.layer.0.feed_forward.dense_1.weight', 'encoder.layer.0.feed_forward.dense_1.bias', 'encoder.layer.0.feed_forward.dense_2.weight', 'encoder.layer.0.feed_forward.dense_2.bias', 'encoder.layer.0.first_layer_norm.weight', 'encoder.layer.0.first_layer_norm.bias', 'encoder.layer.0.second_layer_norm.weight', 'encoder.layer.0.second_layer_norm.bias', 'encoder.layer.1.filter_layer.complex_weight', 'encoder.layer.1.feed_forward.dense_1.weight', 'encoder.layer.1.feed_forward.dense_1.bias', 'encoder.layer.1.feed_forward.dense_2.weight', 'encoder.layer.1.feed_forward.dense_2.bias', 'encoder.layer.1.first_layer_norm.weight', 'encoder.layer.1.first_layer_norm.bias', 'encoder.layer.1.second_layer_norm.weight', 'encoder.layer.1.second_layer_norm.bias']
[Aug-02-2025_22-52-49] - 动态负采样: False
[Aug-02-2025_22-52-49] - 用户数量: 22363
[Aug-02-2025_22-52-49] - 物品数量: 12101
