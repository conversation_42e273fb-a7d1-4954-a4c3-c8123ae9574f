import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import copy
import numpy as np


# ##################################################################
# ############## FMLP-Rec Core Components (Heart) ##################
# ##################################################################

class FilterLayer (nn.Module):
    def __init__ ( self, hidden_size, max_seq_length, hidden_dropout_prob ):
        super (FilterLayer, self).__init__ ()
        self.complex_weight = nn.Parameter (
            torch.randn (1, max_seq_length // 2 + 1, hidden_size, 2, dtype = torch.float32) * 0.02)
        self.out_dropout = nn.Dropout (hidden_dropout_prob)

    def forward ( self, input_tensor ):
        batch, seq_len, hidden = input_tensor.shape
        x = torch.fft.rfft (input_tensor, dim = 1, norm = 'ortho')
        weight = torch.view_as_complex (self.complex_weight)
        x = x * weight
        sequence_emb_fft = torch.fft.irfft (x, n = seq_len, dim = 1, norm = 'ortho')
        return self.out_dropout (sequence_emb_fft)


# ##################################################################
# ############## SASRec Skeleton Components ########################
# ##################################################################

class FeedForward (nn.Module):
    def __init__ ( self, hidden_size, inner_size, hidden_dropout_prob, hidden_act ):
        super (FeedForward, self).__init__ ()
        self.dense_1 = nn.Linear (hidden_size, inner_size)
        self.intermediate_act_fn = self.get_hidden_act (hidden_act)
        self.dense_2 = nn.Linear (inner_size, hidden_size)
        self.dropout = nn.Dropout (hidden_dropout_prob)

    def get_hidden_act ( self, act ):
        return {
            "gelu": lambda x: x * 0.5 * (1.0 + torch.erf (x / math.sqrt (2.0))),
            "relu": F.relu,
            "swish": lambda x: x * torch.sigmoid (x),
        } [act]

    def forward ( self, input_tensor ):
        hidden_states = self.dense_1 (input_tensor)
        hidden_states = self.intermediate_act_fn (hidden_states)
        hidden_states = self.dense_2 (hidden_states)
        return self.dropout (hidden_states)


class FMLPTransformerLayer (nn.Module):
    def __init__ ( self, hidden_size, max_seq_length, inner_size, hidden_dropout_prob, hidden_act, layer_norm_eps ):
        super (FMLPTransformerLayer, self).__init__ ()
        self.filter_layer = FilterLayer (hidden_size, max_seq_length, hidden_dropout_prob)
        self.feed_forward = FeedForward (hidden_size, inner_size, hidden_dropout_prob, hidden_act)
        self.first_layer_norm = nn.LayerNorm (hidden_size, eps = layer_norm_eps)
        self.second_layer_norm = nn.LayerNorm (hidden_size, eps = layer_norm_eps)

    def forward ( self, hidden_states ):
        filter_output = self.filter_layer (hidden_states)
        q = self.first_layer_norm (hidden_states + filter_output)
        ff_output = self.feed_forward (q)
        output = self.second_layer_norm (q + ff_output)
        return output

class TransformerEncoder (nn.Module):
    """
    Transformer编码器
    由多个Transformer层堆叠组成
    支持 Pre-LN 和 Post-LN 架构
    """

    def __init__ (
            self,
            n_layers = 2,  # Transformer层数量
            n_heads = 2,  # 注意力头数量
            hidden_size = 64,  # 隐藏层大小
            inner_size = 256,  # 前馈网络内部大小
            hidden_dropout_prob = 0.5,  # 隐藏层dropout概率
            attn_dropout_prob = 0.5,  # 注意力dropout概率
            hidden_act = "gelu",  # 激活函数类型
            layer_norm_eps = 1e-12,  # Layer Normalization的epsilon值
            norm_first = False ):  # 是否使用Pre-LN架构
        super (TransformerEncoder, self).__init__ ()
        # 创建指定数量的Transformer层
        layer = TransformerLayer (
            n_heads,
            hidden_size,
            inner_size,
            hidden_dropout_prob,
            attn_dropout_prob,
            hidden_act,
            layer_norm_eps,
            norm_first,
        )
        # 使用ModuleList存储多个相同结构的Transformer层
        self.layer = nn.ModuleList ([copy.deepcopy (layer) for _ in range (n_layers)])


class FMLPEncoder (nn.Module):
    def __init__ ( self, n_layers, hidden_size, max_seq_length, inner_size, hidden_dropout_prob, hidden_act,
                   layer_norm_eps ):
        super (FMLPEncoder, self).__init__ ()
        self.layer = nn.ModuleList ([
            FMLPTransformerLayer (hidden_size, max_seq_length, inner_size, hidden_dropout_prob, hidden_act,
                                  layer_norm_eps)
            for _ in range (n_layers)
        ])

    def forward ( self, hidden_states ):
        for layer_module in self.layer:
            hidden_states = layer_module (hidden_states)
        return hidden_states


# ##################################################################
# ########### FMLP-Rec Model (Cloned from SASRec) ##################
# ##################################################################

class FMLPRec (nn.Module):
    """
    A clone of the reference SASRec, where the TransformerEncoder is replaced
    by an FMLPEncoder. All other logic, including function names and structures
    like log2feats and forward_pmixer, is preserved.
    """

    def __init__ ( self, config, itemnum ):
        super (FMLPRec, self).__init__ ()

        self.n_items = itemnum + 1
        self.n_layers = config ['num_layers']
        self.hidden_size = config ['embed_dim']
        self.inner_size = config ['inner_size']
        self.hidden_dropout_prob = config ['dropout']
        self.hidden_act = 'gelu'
        self.layer_norm_eps = 1e-8
        self.max_seq_length = config ['max_seq_len']
        self.device = "cuda" if torch.cuda.is_available () else "cpu"

        self.item_embedding = nn.Embedding (self.n_items, self.hidden_size, padding_idx = 0)
        self.position_embedding = nn.Embedding (self.max_seq_length + 1, self.hidden_size, padding_idx = 0)
        self.emb_dropout = nn.Dropout (self.hidden_dropout_prob)
        self.LayerNorm = nn.LayerNorm (self.hidden_size, eps = self.layer_norm_eps)


        self.encoder = FMLPEncoder (
            n_layers = self.n_layers,
            hidden_size = self.hidden_size,
            max_seq_length = self.max_seq_length,
            inner_size = self.inner_size,
            hidden_dropout_prob = self.hidden_dropout_prob,
            hidden_act = self.hidden_act,
            layer_norm_eps = self.layer_norm_eps
        )

        self.trm_encoder = TransformerEncoder (
            n_layers = self.n_layers,
            n_heads = self.n_heads,
            hidden_size = self.hidden_size,
            inner_size = self.inner_size,
            hidden_dropout_prob = self.hidden_dropout_prob,
            attn_dropout_prob = self.attn_dropout_prob,
            hidden_act = self.hidden_act,
            layer_norm_eps = self.layer_norm_eps,
            norm_first = self.norm_first,  # 传递 norm_first 参数
        )

        self.apply (self.init_weights)

    def init_weights ( self, module ):
        if isinstance (module, (nn.Linear, nn.Embedding)):
            torch.nn.init.xavier_normal_ (module.weight.data)
        if isinstance (module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_ ()

    def get_attention_mask ( self, item_seq, bidirectional = False ):
        """
        生成注意力掩码
        item_seq: 物品序列，形状为(batch_size, seq_len)
        bidirectional: 是否使用双向注意力，默认为False(单向/因果注意力)
        """
        attention_mask = item_seq != 0  # 非0位置为True，表示非padding位置
        extended_attention_mask = attention_mask.unsqueeze (1).unsqueeze (2)  # 扩展维度，变为(batch_size, 1, 1, seq_len)
        if not bidirectional:
            # 使用下三角矩阵，实现因果注意力（当前位置只能看到之前的位置）
            extended_attention_mask = torch.tril (
                extended_attention_mask.expand ((-1, -1, item_seq.size (-1), -1))
            )
        # 将True/False转换为0.0/-10000.0，用于在softmax前屏蔽某些位置
        extended_attention_mask = torch.where (extended_attention_mask, 0.0, -10000.0)
        return extended_attention_mask

    def log2feats ( self, log_seqs ):
        """Replicates the log2feats logic from the reference SASRec."""
        seqs = self.item_embedding (log_seqs)
        seqs *= self.item_embedding.embedding_dim ** 0.5

        poss = np.tile (np.arange (1, log_seqs.shape [1] + 1), [log_seqs.shape [0], 1])
        poss *= (log_seqs.cpu ().numpy () != 0)
        seqs += self.position_embedding (torch.LongTensor (poss).to (self.device))

        seqs = self.emb_dropout (seqs)

        # The only difference: calling FMLPEncoder instead of TransformerEncoder
        log_feats = self.encoder (seqs)

        return log_feats


    def forward ( self, x, x_lens ):
        """
        定义模型的前向传播过程。

        参数:
        - x: 输入的物品序列 (batch_size, seq_len)。
        - x_lens: 每个序列的实际长度 (batch_size)。

        返回:
        - seq_out: 模型对序列中每个位置的下一个物品的预测得分。
        """
        item_seq = x

        # 确保序列长度不超过模型定义的最大长度
        if item_seq.size (1) > self.max_seq_length:
            item_seq = item_seq [:, -self.max_seq_length:]
            # 如果序列被截断，也需要调整序列长度
            x_lens = torch.clamp (x_lens, max = self.max_seq_length)

        # 生成位置编码 - 确保位置ID不超过max_seq_length
        position_ids = torch.arange (
            item_seq.size (1), dtype = torch.long, device = item_seq.device
        )
        position_ids = position_ids.unsqueeze (0).expand_as (item_seq)

        # 确保position_ids不超出范围
        position_ids = torch.clamp (position_ids, 0, self.max_seq_length - 1)
        position_embedding = self.position_embedding (position_ids)

        # 物品嵌入 - 确保item_seq中的ID不超出范围
        item_seq = torch.clamp (item_seq, 0, self.n_items - 1)
        item_emb = self.item_embedding (item_seq)

        # 输入嵌入 = 物品嵌入 + 位置嵌入
        input_emb = item_emb + position_embedding
        input_emb = self.LayerNorm (input_emb)
        input_emb = self.emb_dropout (input_emb)

        # 生成注意力掩码
        extended_attention_mask = self.get_attention_mask (item_seq)

        # Transformer编码
        trm_output = self.trm_encoder (
            input_emb, extended_attention_mask, output_all_encoded_layers = True
        )
        output = trm_output [-1]  # 取最后一层的输出

        # 计算每个物品的预测得分
        seq_output = torch.matmul (output, self.item_embedding.weight.transpose (0, 1))

        return seq_output

    def loss_function(self, seq_out, padding_mask, pos_seq, neg_seq, input_len=None):
        """
        Calculates the loss using the full sequence output (logits).
        This version correctly handles multiple negative samples per positive item.
        """
        # Get logits for positive items: (B, L)
        pos_logits = torch.gather(seq_out, 2, pos_seq.unsqueeze(-1).long()).squeeze(-1)

        # Get logits for negative items: (B, L, N)
        # neg_seq has shape (B, L, N), so it's already 3D. No unsqueeze needed.
        neg_logits = torch.gather(seq_out, 2, neg_seq.long())

        # Calculate loss
        pos_labels = torch.ones_like(pos_logits)
        neg_labels = torch.zeros_like(neg_logits)

        indices = torch.where(pos_seq != 0)
        
        loss = F.binary_cross_entropy_with_logits(
            pos_logits[indices], pos_labels[indices]
        )
        
        # Correctly handle loss for multiple negatives
        # Reshape to (B*L, N) and find valid indices
        valid_neg_logits = neg_logits[indices]
        valid_neg_labels = neg_labels[indices]
        
        loss += F.binary_cross_entropy_with_logits(
            valid_neg_logits, valid_neg_labels
        )
        
        return loss

    def predict ( self, user_ids, log_seqs, item_indices ):
        """
        Replicates the predict logic from the reference SASRec.
        """
        self.eval ()
        with torch.no_grad ():
            log_seqs_tensor = torch.from_numpy (log_seqs).long ().to (self.device)
            item_indices_tensor = torch.from_numpy (item_indices).long ().to (self.device)

            log_feats = self.log2feats (log_seqs_tensor)
            final_feat = log_feats [:, -1, :]

            item_embs = self.item_embedding (item_indices_tensor)
            logits = item_embs.matmul (final_feat.unsqueeze (-1)).squeeze (-1)

            return logits.cpu ().numpy ()
