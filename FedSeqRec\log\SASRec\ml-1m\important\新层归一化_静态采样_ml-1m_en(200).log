[Jul-23-2025_09-28-31] - epoch:11, time: 116.644298(s), valid (NDCG@10: 0.4175, HR@10: 0.6743), test: SKIPPED, all_time: 1279.168334(s)
[Jul-23-2025_09-28-31] - 新的最佳性能: valid NDCG@10=0.4183, valid HR@10=0.6743

{'early_stop_enabled': True, 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': True}
[Jul-23-2025_09-01-41] - 开始训练，配置参数如下：
[Jul-23-2025_09-01-41] - early_stop_enabled: True
[Jul-23-2025_09-01-41] - lr: 0.001
[Jul-23-2025_09-01-41] - batch_size: 128
[Jul-23-2025_09-01-41] - l2_reg: 0
[Jul-23-2025_09-01-41] - l2_emb: 0.0
[Jul-23-2025_09-01-41] - embed_dim: 50
[Jul-23-2025_09-01-41] - hidden_size: 32
[Jul-23-2025_09-01-41] - dropout: 0.2
[Jul-23-2025_09-01-41] - epochs: 1000000
[Jul-23-2025_09-01-41] - early_stop: 50
[Jul-23-2025_09-01-41] - datapath: ../../data/
[Jul-23-2025_09-01-41] - dataset: ml-1m
[Jul-23-2025_09-01-41] - train_data: ml-1m.txt
[Jul-23-2025_09-01-41] - log_path: ../log
[Jul-23-2025_09-01-41] - num_layers: 2
[Jul-23-2025_09-01-41] - num_heads: 1
[Jul-23-2025_09-01-41] - inner_size: 256
[Jul-23-2025_09-01-41] - max_seq_len: 200
[Jul-23-2025_09-01-41] - upload_mode: full
[Jul-23-2025_09-01-41] - skip_test_eval: True
[Jul-23-2025_09-01-41] - eval_freq: 1
[Jul-23-2025_09-01-41] - use_dynamic_sampling: False
[Jul-23-2025_09-01-41] - norm_first: True
[Jul-23-2025_09-01-41] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Jul-23-2025_09-01-41] - 最大序列长度: 200
[Jul-23-2025_09-01-41] - 批次大小: 128
[Jul-23-2025_09-01-44] - 参数上传模式: full
[Jul-23-2025_09-01-44] - 隐私参数（本地更新）: []
[Jul-23-2025_09-01-44] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'trm_encoder.layer.0.multi_head_attention.query.weight', 'trm_encoder.layer.0.multi_head_attention.query.bias', 'trm_encoder.layer.0.multi_head_attention.key.weight', 'trm_encoder.layer.0.multi_head_attention.key.bias', 'trm_encoder.layer.0.multi_head_attention.value.weight', 'trm_encoder.layer.0.multi_head_attention.value.bias', 'trm_encoder.layer.0.multi_head_attention.dense.weight', 'trm_encoder.layer.0.multi_head_attention.dense.bias', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.0.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.0.feed_forward.dense_1.weight', 'trm_encoder.layer.0.feed_forward.dense_1.bias', 'trm_encoder.layer.0.feed_forward.dense_2.weight', 'trm_encoder.layer.0.feed_forward.dense_2.bias', 'trm_encoder.layer.0.feed_forward.LayerNorm.weight', 'trm_encoder.layer.0.feed_forward.LayerNorm.bias', 'trm_encoder.layer.1.multi_head_attention.query.weight', 'trm_encoder.layer.1.multi_head_attention.query.bias', 'trm_encoder.layer.1.multi_head_attention.key.weight', 'trm_encoder.layer.1.multi_head_attention.key.bias', 'trm_encoder.layer.1.multi_head_attention.value.weight', 'trm_encoder.layer.1.multi_head_attention.value.bias', 'trm_encoder.layer.1.multi_head_attention.dense.weight', 'trm_encoder.layer.1.multi_head_attention.dense.bias', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.weight', 'trm_encoder.layer.1.multi_head_attention.LayerNorm.bias', 'trm_encoder.layer.1.feed_forward.dense_1.weight', 'trm_encoder.layer.1.feed_forward.dense_1.bias', 'trm_encoder.layer.1.feed_forward.dense_2.weight', 'trm_encoder.layer.1.feed_forward.dense_2.bias', 'trm_encoder.layer.1.feed_forward.LayerNorm.weight', 'trm_encoder.layer.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Jul-23-2025_09-01-44] - 动态负采样: False
[Jul-23-2025_09-01-44] - 用户数量: 6040
[Jul-23-2025_09-01-44] - 物品数量: 3416
[Jul-23-2025_09-04-06] - epoch:1, time: 112.032888(s), valid (NDCG@10: 0.2392, HR@10: 0.4328), test: SKIPPED, all_time: 112.032888(s)
[Jul-23-2025_09-04-06] - 新的最佳性能: valid NDCG@10=0.2392, valid HR@10=0.4328
[Jul-23-2025_09-06-31] - epoch:2, time: 115.990187(s), valid (NDCG@10: 0.2378, HR@10: 0.4344), test: SKIPPED, all_time: 228.023075(s)
[Jul-23-2025_09-06-31] - 新的最佳性能: valid NDCG@10=0.2392, valid HR@10=0.4344
[Jul-23-2025_09-08-57] - epoch:3, time: 116.297801(s), valid (NDCG@10: 0.2948, HR@10: 0.5280), test: SKIPPED, all_time: 344.320876(s)
[Jul-23-2025_09-08-57] - 新的最佳性能: valid NDCG@10=0.2948, valid HR@10=0.5280
[Jul-23-2025_09-11-24] - epoch:4, time: 116.722373(s), valid (NDCG@10: 0.3553, HR@10: 0.6030), test: SKIPPED, all_time: 461.043248(s)
[Jul-23-2025_09-11-24] - 新的最佳性能: valid NDCG@10=0.3553, valid HR@10=0.6030
[Jul-23-2025_09-13-50] - epoch:5, time: 116.543909(s), valid (NDCG@10: 0.3797, HR@10: 0.6377), test: SKIPPED, all_time: 577.587157(s)
[Jul-23-2025_09-13-50] - 新的最佳性能: valid NDCG@10=0.3797, valid HR@10=0.6377
[Jul-23-2025_09-16-17] - epoch:6, time: 116.580047(s), valid (NDCG@10: 0.4016, HR@10: 0.6513), test: SKIPPED, all_time: 694.167204(s)
[Jul-23-2025_09-16-17] - 新的最佳性能: valid NDCG@10=0.4016, valid HR@10=0.6513
[Jul-23-2025_09-18-45] - epoch:7, time: 118.330146(s), valid (NDCG@10: 0.4091, HR@10: 0.6656), test: SKIPPED, all_time: 812.497350(s)
[Jul-23-2025_09-18-45] - 新的最佳性能: valid NDCG@10=0.4091, valid HR@10=0.6656
[Jul-23-2025_09-21-12] - epoch:8, time: 116.898095(s), valid (NDCG@10: 0.4104, HR@10: 0.6689), test: SKIPPED, all_time: 929.395446(s)
[Jul-23-2025_09-21-12] - 新的最佳性能: valid NDCG@10=0.4104, valid HR@10=0.6689
[Jul-23-2025_09-23-38] - epoch:9, time: 116.550756(s), valid (NDCG@10: 0.4173, HR@10: 0.6728), test: SKIPPED, all_time: 1045.946201(s)
[Jul-23-2025_09-23-38] - 新的最佳性能: valid NDCG@10=0.4173, valid HR@10=0.6728
[Jul-23-2025_09-26-05] - epoch:10, time: 116.577834(s), valid (NDCG@10: 0.4183, HR@10: 0.6735), test: SKIPPED, all_time: 1162.524036(s)
[Jul-23-2025_09-26-05] - 新的最佳性能: valid NDCG@10=0.4183, valid HR@10=0.6735
[Jul-23-2025_09-28-31] - epoch:11, time: 116.644298(s), valid (NDCG@10: 0.4175, HR@10: 0.6743), test: SKIPPED, all_time: 1279.168334(s)
[Jul-23-2025_09-28-31] - 新的最佳性能: valid NDCG@10=0.4183, valid HR@10=0.6743
[Jul-23-2025_09-31-07] - epoch:12, time: 125.110755(s), valid (NDCG@10: 0.4121, HR@10: 0.6639), test: SKIPPED, all_time: 1404.279089(s)
[Jul-23-2025_09-33-35] - epoch:13, time: 118.302402(s), valid (NDCG@10: 0.4085, HR@10: 0.6637), test: SKIPPED, all_time: 1522.581491(s)
[Jul-23-2025_09-36-02] - epoch:14, time: 117.070305(s), valid (NDCG@10: 0.4065, HR@10: 0.6581), test: SKIPPED, all_time: 1639.651797(s)
[Jul-23-2025_09-38-29] - epoch:15, time: 116.559048(s), valid (NDCG@10: 0.4085, HR@10: 0.6629), test: SKIPPED, all_time: 1756.210845(s)
[Jul-23-2025_09-40-56] - epoch:16, time: 117.319264(s), valid (NDCG@10: 0.4067, HR@10: 0.6604), test: SKIPPED, all_time: 1873.530108(s)
[Jul-23-2025_09-43-23] - epoch:17, time: 116.749839(s), valid (NDCG@10: 0.4070, HR@10: 0.6584), test: SKIPPED, all_time: 1990.279948(s)
[Jul-23-2025_09-45-53] - epoch:18, time: 118.675782(s), valid (NDCG@10: 0.4075, HR@10: 0.6568), test: SKIPPED, all_time: 2108.955729(s)
[Jul-23-2025_09-48-23] - epoch:19, time: 119.708250(s), valid (NDCG@10: 0.4043, HR@10: 0.6591), test: SKIPPED, all_time: 2228.663979(s)
[Jul-23-2025_09-50-54] - epoch:20, time: 119.469746(s), valid (NDCG@10: 0.4075, HR@10: 0.6581), test: SKIPPED, all_time: 2348.133725(s)
[Jul-23-2025_09-53-23] - epoch:21, time: 119.052715(s), valid (NDCG@10: 0.4047, HR@10: 0.6568), test: SKIPPED, all_time: 2467.186440(s)
[Jul-23-2025_09-55-55] - epoch:22, time: 120.579185(s), valid (NDCG@10: 0.4055, HR@10: 0.6546), test: SKIPPED, all_time: 2587.765625(s)
[Jul-23-2025_09-58-24] - epoch:23, time: 118.899060(s), valid (NDCG@10: 0.4076, HR@10: 0.6588), test: SKIPPED, all_time: 2706.664685(s)
[Jul-23-2025_10-00-54] - epoch:24, time: 119.813449(s), valid (NDCG@10: 0.3979, HR@10: 0.6556), test: SKIPPED, all_time: 2826.478134(s)
[Jul-23-2025_10-03-24] - epoch:25, time: 119.521392(s), valid (NDCG@10: 0.4054, HR@10: 0.6629), test: SKIPPED, all_time: 2945.999526(s)
[Jul-23-2025_10-05-54] - epoch:26, time: 119.267505(s), valid (NDCG@10: 0.4046, HR@10: 0.6613), test: SKIPPED, all_time: 3065.267030(s)
[Jul-23-2025_10-08-24] - epoch:27, time: 119.217048(s), valid (NDCG@10: 0.4050, HR@10: 0.6632), test: SKIPPED, all_time: 3184.484078(s)
[Jul-23-2025_10-10-55] - epoch:28, time: 119.131618(s), valid (NDCG@10: 0.4056, HR@10: 0.6637), test: SKIPPED, all_time: 3303.615696(s)
[Jul-23-2025_10-13-24] - epoch:29, time: 119.168020(s), valid (NDCG@10: 0.4070, HR@10: 0.6649), test: SKIPPED, all_time: 3422.783716(s)
[Jul-23-2025_10-15-54] - epoch:30, time: 119.081398(s), valid (NDCG@10: 0.4056, HR@10: 0.6652), test: SKIPPED, all_time: 3541.865114(s)
[Jul-23-2025_10-18-26] - epoch:31, time: 120.836279(s), valid (NDCG@10: 0.4042, HR@10: 0.6619), test: SKIPPED, all_time: 3662.701393(s)
[Jul-23-2025_10-20-56] - epoch:32, time: 119.010059(s), valid (NDCG@10: 0.4076, HR@10: 0.6684), test: SKIPPED, all_time: 3781.711452(s)
[Jul-23-2025_10-23-25] - epoch:33, time: 119.119785(s), valid (NDCG@10: 0.4100, HR@10: 0.6634), test: SKIPPED, all_time: 3900.831237(s)
[Jul-23-2025_10-25-55] - epoch:34, time: 119.250535(s), valid (NDCG@10: 0.4076, HR@10: 0.6604), test: SKIPPED, all_time: 4020.081772(s)
[Jul-23-2025_10-28-25] - epoch:35, time: 119.015821(s), valid (NDCG@10: 0.4027, HR@10: 0.6624), test: SKIPPED, all_time: 4139.097593(s)
[Jul-23-2025_10-30-56] - epoch:36, time: 119.749413(s), valid (NDCG@10: 0.4048, HR@10: 0.6616), test: SKIPPED, all_time: 4258.847006(s)
[Jul-23-2025_10-33-26] - epoch:37, time: 119.430515(s), valid (NDCG@10: 0.4067, HR@10: 0.6604), test: SKIPPED, all_time: 4378.277521(s)
[Jul-23-2025_10-35-57] - epoch:38, time: 119.576997(s), valid (NDCG@10: 0.4071, HR@10: 0.6636), test: SKIPPED, all_time: 4497.854519(s)
[Jul-23-2025_10-38-27] - epoch:39, time: 119.367683(s), valid (NDCG@10: 0.4012, HR@10: 0.6568), test: SKIPPED, all_time: 4617.222202(s)
[Jul-23-2025_10-40-58] - epoch:40, time: 119.926353(s), valid (NDCG@10: 0.4033, HR@10: 0.6584), test: SKIPPED, all_time: 4737.148555(s)
[Jul-23-2025_10-43-28] - epoch:41, time: 119.372933(s), valid (NDCG@10: 0.3990, HR@10: 0.6508), test: SKIPPED, all_time: 4856.521488(s)
[Jul-23-2025_10-45-58] - epoch:42, time: 119.143706(s), valid (NDCG@10: 0.4015, HR@10: 0.6558), test: SKIPPED, all_time: 4975.665194(s)
[Jul-23-2025_10-48-28] - epoch:43, time: 119.277253(s), valid (NDCG@10: 0.3964, HR@10: 0.6503), test: SKIPPED, all_time: 5094.942446(s)
[Jul-23-2025_10-50-58] - epoch:44, time: 119.784476(s), valid (NDCG@10: 0.3936, HR@10: 0.6488), test: SKIPPED, all_time: 5214.726923(s)
[Jul-23-2025_10-53-28] - epoch:45, time: 119.108498(s), valid (NDCG@10: 0.3956, HR@10: 0.6507), test: SKIPPED, all_time: 5333.835420(s)
[Jul-23-2025_10-55-59] - epoch:46, time: 120.809983(s), valid (NDCG@10: 0.3944, HR@10: 0.6474), test: SKIPPED, all_time: 5454.645403(s)
[Jul-23-2025_10-58-29] - epoch:47, time: 119.226770(s), valid (NDCG@10: 0.3916, HR@10: 0.6454), test: SKIPPED, all_time: 5573.872173(s)
[Jul-23-2025_11-01-00] - epoch:48, time: 119.651031(s), valid (NDCG@10: 0.3929, HR@10: 0.6510), test: SKIPPED, all_time: 5693.523204(s)
[Jul-23-2025_11-03-30] - epoch:49, time: 119.945925(s), valid (NDCG@10: 0.3887, HR@10: 0.6427), test: SKIPPED, all_time: 5813.469129(s)
[Jul-23-2025_11-06-01] - epoch:50, time: 119.551350(s), valid (NDCG@10: 0.3865, HR@10: 0.6394), test: SKIPPED, all_time: 5933.020479(s)
[Jul-23-2025_11-08-31] - epoch:51, time: 119.435943(s), valid (NDCG@10: 0.3857, HR@10: 0.6358), test: SKIPPED, all_time: 6052.456422(s)
[Jul-23-2025_11-11-01] - epoch:52, time: 119.590762(s), valid (NDCG@10: 0.3786, HR@10: 0.6318), test: SKIPPED, all_time: 6172.047184(s)
[Jul-23-2025_11-13-31] - epoch:53, time: 119.068667(s), valid (NDCG@10: 0.3801, HR@10: 0.6285), test: SKIPPED, all_time: 6291.115850(s)
[Jul-23-2025_11-16-01] - epoch:54, time: 119.035608(s), valid (NDCG@10: 0.3874, HR@10: 0.6384), test: SKIPPED, all_time: 6410.151458(s)
[Jul-23-2025_11-18-33] - epoch:55, time: 120.920424(s), valid (NDCG@10: 0.3803, HR@10: 0.6308), test: SKIPPED, all_time: 6531.071882(s)
[Jul-23-2025_11-21-02] - epoch:56, time: 119.166040(s), valid (NDCG@10: 0.3845, HR@10: 0.6334), test: SKIPPED, all_time: 6650.237922(s)
[Jul-23-2025_11-23-33] - epoch:57, time: 119.477693(s), valid (NDCG@10: 0.3770, HR@10: 0.6300), test: SKIPPED, all_time: 6769.715615(s)
[Jul-23-2025_11-26-03] - epoch:58, time: 119.270305(s), valid (NDCG@10: 0.3800, HR@10: 0.6308), test: SKIPPED, all_time: 6888.985920(s)
[Jul-23-2025_11-28-32] - epoch:59, time: 119.178959(s), valid (NDCG@10: 0.3724, HR@10: 0.6243), test: SKIPPED, all_time: 7008.164879(s)
[Jul-23-2025_11-31-04] - 早停触发！NDCG在50轮内没有改善。
[Jul-23-2025_11-31-04] - epoch:60, time: 120.137291(s), valid (NDCG@10: 0.3758, HR@10: 0.6290), test: SKIPPED, all_time: 7128.302171(s)
[Jul-23-2025_11-31-04] - [联邦训练] 最佳结果: valid NDCG@10=0.4183, HR@10=0.6743 (测试集评估已跳过)
