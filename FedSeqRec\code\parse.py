import argparse
import yaml

def parse_args():
    parser = argparse.ArgumentParser()

    # 首先加载config文件
    config = yaml.safe_load (open ('./config/basic.yaml', 'r'))

    parser.add_argument('--model',type = str,default ='FMLP' ,choices = ['SASRec','FMLP'],help='选择推荐模型')

    # 调整为与参考项目SAS.torch一致的参数配置
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate (参考SAS.torch: 0.001)')
    parser.add_argument('--batch_size', type=int, default=128, help='Batch size (参考SAS.torch: 128)')
    parser.add_argument ('--neg_num', type = int, default = 99, help = '负样本采样数量')
    parser.add_argument('--l2_reg', type=float, default=0, help='L2 regularization coefficient')
    parser.add_argument('--l2_emb', type=float, default=0.0, help='L2 regularization for embeddings (参考SAS.torch: 0.0)')
    parser.add_argument('--embed_dim', type=int, default=50,
                        help='embedding vector dimensionality (参考SAS.torch hidden_units: 50)')
    parser.add_argument('--hidden_size', type=int, default=32, help='hidden layer dimensionality.')
    parser.add_argument('--dropout', type=float, default=0.2, help='dropout rate (参考SAS.torch: 0.2)')
    parser.add_argument('--epochs', type=int, default=1000000, help='训练轮数 ')
    parser.add_argument('--early_stop', type=int, default=50, help='Patience for early stop')
    parser.add_argument('--datapath', type=str, default='../../data/', help='Data path')
    parser.add_argument('--dataset', type=str, default='ml-100k', help='Dataset name')
    parser.add_argument('--train_data', type=str, default='ml-100k.txt', help='train dataset')
    parser.add_argument ('--log_path', type = str, default = '../log', help = 'logger path')
    parser.add_argument('--num_layers', type=int, default=2, help='Transformer层数 (参考SAS.torch num_blocks: 2)')
    parser.add_argument('--num_heads', type=int, default=1, help='注意力头数量 (参考SAS.torch: 1)')
    parser.add_argument('--inner_size', type=int, default=256, help='前馈网络内部大小')
    parser.add_argument('--max_seq_len', type=int, default=200, help='最大序列长度 (参考SAS.torch maxlen: 200)')


    # 参数上传模式
    parser.add_argument('--upload_mode', type=str, default='full',
                        choices=['partial', 'full'],
                        help='参数上传模式: partial(部分上传-仅embedding,隐私参数本地训练), full(全部上传到服务器)')

    # 测试集评估控制参数
    parser.add_argument('--skip_test_eval', action='store_true', default=True,
                        help='跳过测试集评估以节省训练时间，只进行验证集评估')

    # 评估频率控制参数
    parser.add_argument('--eval_freq', type=int, default=1,
                        help='每隔多少个epoch进行一次评估，默认为20')

    #是否使用动态采样(会让训练时间翻倍)
    parser.add_argument('--use_dynamic_sampling', action='store_true', default=False,
                        help='是否使用动态负采样')

    parser.add_argument("--norm_first", action="store_true", default=False,
                        help="是否使用 Pre-LN 架构 即SASRec.torch特别修改的")

    args = parser.parse_args ()
    config.update (vars (args))  # 使用这个字典更新之前从 YAML 文件加载的配置字典 config

    return config

config = parse_args()