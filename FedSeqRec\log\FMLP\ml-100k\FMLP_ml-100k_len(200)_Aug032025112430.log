{'early_stop_enabled': True, 'model': 'FMLP', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False}
[Aug-03-2025_11-24-30] - 开始训练，配置参数如下：
[Aug-03-2025_11-24-30] - early_stop_enabled: True
[Aug-03-2025_11-24-30] - model: FMLP
[Aug-03-2025_11-24-30] - lr: 0.001
[Aug-03-2025_11-24-30] - batch_size: 128
[Aug-03-2025_11-24-30] - neg_num: 99
[Aug-03-2025_11-24-30] - l2_reg: 0
[Aug-03-2025_11-24-30] - l2_emb: 0.0
[Aug-03-2025_11-24-30] - embed_dim: 50
[Aug-03-2025_11-24-30] - hidden_size: 32
[Aug-03-2025_11-24-30] - dropout: 0.2
[Aug-03-2025_11-24-30] - epochs: 1000000
[Aug-03-2025_11-24-30] - early_stop: 50
[Aug-03-2025_11-24-30] - datapath: ../../data/
[Aug-03-2025_11-24-30] - dataset: ml-100k
[Aug-03-2025_11-24-30] - train_data: ml-100k.txt
[Aug-03-2025_11-24-30] - log_path: ../log
[Aug-03-2025_11-24-30] - num_layers: 2
[Aug-03-2025_11-24-30] - num_heads: 1
[Aug-03-2025_11-24-30] - inner_size: 256
[Aug-03-2025_11-24-30] - max_seq_len: 200
[Aug-03-2025_11-24-30] - upload_mode: full
[Aug-03-2025_11-24-30] - skip_test_eval: True
[Aug-03-2025_11-24-30] - eval_freq: 1
[Aug-03-2025_11-24-30] - use_dynamic_sampling: False
[Aug-03-2025_11-24-30] - norm_first: False
[Aug-03-2025_11-24-30] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-03-2025_11-24-30] - 最大序列长度: 200
[Aug-03-2025_11-24-30] - 批次大小: 128
